/*
 Navicat Premium Data Transfer

 Source Server         : root
 Source Server Type    : MySQL
 Source Server Version : 50734
 Source Host           : localhost:3306
 Source Schema         : salary

 Target Server Type    : MySQL
 Target Server Version : 50734
 File Encoding         : 65001

 Date: 14/10/2024 22:02:13
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for department
-- ----------------------------
DROP TABLE IF EXISTS `department`;
CREATE TABLE `department`  (
  `dname` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `vocationsalary` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`dname`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of department
-- ----------------------------
INSERT INTO `department` VALUES ('java开发', 20000);
INSERT INTO `department` VALUES ('技术', 500);
INSERT INTO `department` VALUES ('海外', 2555);
INSERT INTO `department` VALUES ('爬虫开发', 13444);
INSERT INTO `department` VALUES ('研发', 8100);

-- ----------------------------
-- Table structure for employee
-- ----------------------------
DROP TABLE IF EXISTS `employee`;
CREATE TABLE `employee`  (
  `eno` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `ename` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `edept` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `esex` char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `egrade` char(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `erank` char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `eage` int(11) NULL DEFAULT NULL,
  `ewelfare` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`eno`) USING BTREE,
  INDEX `ename`(`ename`) USING BTREE,
  INDEX `eno`(`eno`, `ename`) USING BTREE,
  INDEX `de`(`edept`) USING BTREE,
  CONSTRAINT `de` FOREIGN KEY (`edept`) REFERENCES `department` (`dname`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of employee
-- ----------------------------
INSERT INTO `employee` VALUES ('1', '管理员', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `employee` VALUES ('2020', '王冰水', 'java开发', '男', '主管', 'A', 18, '加班补贴');
INSERT INTO `employee` VALUES ('2021', '黄鱼', '技术', '男', '主管', 'A', 24, '节日');
INSERT INTO `employee` VALUES ('2022', '彭宇呀', '技术', '男', '实习生', 'A', 24, '差旅');
INSERT INTO `employee` VALUES ('2024', '小新', 'java开发', '男', '主管', 'A', 22, '自定义补贴');

-- ----------------------------
-- Table structure for salary
-- ----------------------------
DROP TABLE IF EXISTS `salary`;
CREATE TABLE `salary`  (
  `sno` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `sname` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `basicsalary` int(11) NOT NULL DEFAULT 0,
  `vocationsalary` int(11) NOT NULL DEFAULT 0,
  `agesalary` int(11) NOT NULL DEFAULT 0,
  `welfare` int(11) NOT NULL DEFAULT 0,
  `allsalary` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`sno`, `sname`) USING BTREE,
  INDEX `index_salary`(`sno`) USING BTREE,
  CONSTRAINT `sno_r` FOREIGN KEY (`sno`, `sname`) REFERENCES `employee` (`eno`, `ename`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of salary
-- ----------------------------
INSERT INTO `salary` VALUES ('2020', '王冰水', 8000, 1200, 10800, 200, 20200);
INSERT INTO `salary` VALUES ('2021', '黄鱼', 500, 1200, 14400, 500, 16600);
INSERT INTO `salary` VALUES ('2022', '彭宇呀', 500, 300, 14400, 900, 16100);
INSERT INTO `salary` VALUES ('2024', '小新', 20000, 1200, 13200, 900, 35300);

-- ----------------------------
-- Table structure for salarygrade
-- ----------------------------
DROP TABLE IF EXISTS `salarygrade`;
CREATE TABLE `salarygrade`  (
  `grade` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `gradesalary` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`grade`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of salarygrade
-- ----------------------------
INSERT INTO `salarygrade` VALUES ('主管', 1200);
INSERT INTO `salarygrade` VALUES ('员工', 600);
INSERT INTO `salarygrade` VALUES ('实习生', 300);
INSERT INTO `salarygrade` VALUES ('组长', 800);
INSERT INTO `salarygrade` VALUES ('经理', 1500);

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` char(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `username` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `password` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username`) USING BTREE,
  CONSTRAINT `id` FOREIGN KEY (`id`) REFERENCES `employee` (`eno`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user
-- ----------------------------
INSERT INTO `user` VALUES ('1', 'root', '123456');
INSERT INTO `user` VALUES ('2020', '2020', '123456');
INSERT INTO `user` VALUES ('2021', '2021', '123456');
INSERT INTO `user` VALUES ('2022', '2022', '123456');
INSERT INTO `user` VALUES ('2024', '2024', '123456');

-- ----------------------------
-- Table structure for welfare
-- ----------------------------
DROP TABLE IF EXISTS `welfare`;
CREATE TABLE `welfare`  (
  `wname` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `wsalary` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`wname`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of welfare
-- ----------------------------
INSERT INTO `welfare` VALUES ('低温补贴', 22221);
INSERT INTO `welfare` VALUES ('加班补贴', 200);
INSERT INTO `welfare` VALUES ('网费', 50);
INSERT INTO `welfare` VALUES ('自定义补贴', 900);
INSERT INTO `welfare` VALUES ('节日', 500);
INSERT INTO `welfare` VALUES ('话费补贴', 50);

-- ----------------------------
-- View structure for v_all
-- ----------------------------
DROP VIEW IF EXISTS `v_all`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_all` AS select `employee`.`eno` AS `eno`,`employee`.`ename` AS `ename`,`employee`.`edept` AS `edept`,`employee`.`esex` AS `esex`,`employee`.`egrade` AS `egrade`,`employee`.`erank` AS `erank`,`employee`.`eage` AS `eage`,`employee`.`ewelfare` AS `ewelfare`,`salary`.`sno` AS `sno`,`salary`.`sname` AS `sname`,`salary`.`basicsalary` AS `basicsalary`,`salary`.`vocationsalary` AS `vocationsalary`,`salary`.`agesalary` AS `agesalary`,`salary`.`welfare` AS `welfare`,`salary`.`allsalary` AS `allsalary` from (`employee` join `salary`) where (`employee`.`eno` = `salary`.`sno`)  WITH CASCADED CHECK OPTION;

-- ----------------------------
-- Triggers structure for table employee
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_userbegin`;
delimiter ;;
CREATE TRIGGER `tr_userbegin` AFTER INSERT ON `employee` FOR EACH ROW INSERT INTO user (id,username,password) VALUES (new.eno+0,new.eno,123456)
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table employee
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_salarybegin`;
delimiter ;;
CREATE TRIGGER `tr_salarybegin` AFTER INSERT ON `employee` FOR EACH ROW INSERT INTO salary (sno,sname) VALUES (new.eno,new.ename)
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table employee
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_welfaresalary`;
delimiter ;;
CREATE TRIGGER `tr_welfaresalary` AFTER UPDATE ON `employee` FOR EACH ROW UPDATE salary,employee,welfare SET salary.welfare = welfare.wsalary
WHERE salary.sno = employee.eno AND employee.ewelfare = welfare.wname
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table employee
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_basicsalary`;
delimiter ;;
CREATE TRIGGER `tr_basicsalary` AFTER UPDATE ON `employee` FOR EACH ROW UPDATE salary,employee,department SET salary.basicsalary = department.vocationsalary
WHERE employee.edept = department.dname AND salary.sno = employee.eno
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table employee
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_vocationsalary`;
delimiter ;;
CREATE TRIGGER `tr_vocationsalary` AFTER UPDATE ON `employee` FOR EACH ROW UPDATE salary,employee,salarygrade SET salary.vocationsalary = salarygrade.gradesalary
WHERE employee.egrade = salarygrade.grade AND salary.sno = employee.eno
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table employee
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_agesalary`;
delimiter ;;
CREATE TRIGGER `tr_agesalary` AFTER UPDATE ON `employee` FOR EACH ROW UPDATE salary,employee SET salary.agesalary = employee.eage * 600
WHERE salary.sno = employee.eno
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table salary
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_allsalary`;
delimiter ;;
CREATE TRIGGER `tr_allsalary` BEFORE UPDATE ON `salary` FOR EACH ROW SET new.allsalary = new.basicsalary+new.vocationsalary+new.agesalary+new.welfare
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table user
-- ----------------------------
DROP TRIGGER IF EXISTS `updateuser`;
delimiter ;;
CREATE TRIGGER `updateuser` BEFORE UPDATE ON `user` FOR EACH ROW UPDATE `user`
SET username = id
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
